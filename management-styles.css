/* Game Management System Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.management-container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.management-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.management-header h1 {
    font-size: 2.2em;
    margin-bottom: 15px;
    text-align: center;
}

.management-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
    font-weight: 500;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    border-color: white;
}

/* Main Content */
.management-content {
    padding: 30px;
}

.management-section {
    display: none;
}

.management-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.section-header h2 {
    font-size: 2em;
    color: #2c3e50;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.9em;
}

/* Question Management Styles */
.question-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-filters select,
.question-filters input {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.question-filters select:focus,
.question-filters input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.questions-list {
    display: grid;
    gap: 15px;
}

.question-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.question-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.question-text {
    font-size: 1.1em;
    font-weight: 500;
    color: #2c3e50;
    flex: 1;
    margin-right: 15px;
}

.question-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.question-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
    text-transform: uppercase;
}

.badge-level-1 { background: #d5f4e6; color: #27ae60; }
.badge-level-2 { background: #fef9e7; color: #f39c12; }
.badge-level-3 { background: #fadbd8; color: #e74c3c; }

.badge-multiple-choice { background: #ebf3fd; color: #3498db; }
.badge-true-false { background: #f0e6ff; color: #9b59b6; }
.badge-practical { background: #e8f5e8; color: #27ae60; }

.question-controls {
    display: flex;
    gap: 10px;
}

/* Level Management Styles */
.levels-list {
    display: grid;
    gap: 20px;
}

.level-item {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #3498db;
    transition: all 0.3s ease;
}

.level-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.level-info h4 {
    font-size: 1.3em;
    color: #2c3e50;
    margin-bottom: 8px;
}

.level-info p {
    color: #7f8c8d;
    margin-bottom: 12px;
    line-height: 1.5;
}

.level-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.level-badge.beginner { background: #d5f4e6; color: #27ae60; }
.level-badge.intermediate { background: #fef9e7; color: #f39c12; }
.level-badge.advanced { background: #fadbd8; color: #e74c3c; }
.level-badge.expert { background: #e8e8f3; color: #8e44ad; }

.level-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

.position-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.position-controls label {
    font-size: 0.9em;
    color: #7f8c8d;
    font-weight: 500;
}

.position-selector {
    padding: 6px 10px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.position-selector:focus {
    outline: none;
    border-color: #3498db;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.4em;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form Styles */
form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

/* Settings Styles */
.settings-container {
    max-width: 800px;
}

.setting-group {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.setting-group h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .management-nav {
        flex-direction: column;
        gap: 5px;
    }
    
    .nav-btn {
        padding: 10px 15px;
        font-size: 0.9em;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .question-filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .level-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .position-controls {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
