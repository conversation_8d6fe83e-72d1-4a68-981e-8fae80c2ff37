// Game Management System JavaScript
class GameManagement {
    constructor() {
        this.currentSection = 'questions';
        this.questions = this.loadSampleQuestions();
        this.levels = this.loadSampleLevels();
        this.editingQuestion = null;
        this.editingLevel = null;
        this.isEditing = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderQuestions();
        this.renderLevels();
        this.showSection('questions');
    }

    bindEvents() {
        // Navigation events
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.showSection(section);
            });
        });

        // Question management events
        document.getElementById('add-question-btn').addEventListener('click', () => this.openQuestionEditor());
        document.getElementById('close-editor').addEventListener('click', () => this.closeQuestionEditor());
        document.getElementById('cancel-edit').addEventListener('click', () => this.closeQuestionEditor());
        document.getElementById('question-form').addEventListener('submit', (e) => this.saveQuestion(e));
        document.getElementById('question-form').addEventListener('input', () => this.showSaveButton());
        document.getElementById('question-form').addEventListener('change', () => this.showSaveButton());

        // Level management events
        document.getElementById('add-level-btn').addEventListener('click', () => this.openLevelEditor());
        document.getElementById('close-level-editor').addEventListener('click', () => this.closeLevelEditor());
        document.getElementById('cancel-level-edit').addEventListener('click', () => this.closeLevelEditor());
        document.getElementById('level-form').addEventListener('submit', (e) => this.saveLevel(e));
        document.getElementById('level-form').addEventListener('input', () => this.showLevelSaveButton());
        document.getElementById('level-form').addEventListener('change', () => this.showLevelSaveButton());

        // Search and filter events
        document.getElementById('search-questions').addEventListener('input', (e) => this.filterQuestions(e.target.value));
        document.getElementById('level-filter').addEventListener('change', (e) => this.filterQuestionsByLevel(e.target.value));

        // Modal close events
        document.getElementById('question-editor-modal').addEventListener('click', (e) => {
            if (e.target.id === 'question-editor-modal') this.closeQuestionEditor();
        });
        document.getElementById('level-editor-modal').addEventListener('click', (e) => {
            if (e.target.id === 'level-editor-modal') this.closeLevelEditor();
        });

        // Dynamic choice management
        document.getElementById('add-choice-btn').addEventListener('click', () => this.addChoice());
        document.getElementById('question-type').addEventListener('change', (e) => this.handleQuestionTypeChange(e.target.value));
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.management-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        this.currentSection = sectionName;
    }

    // Question Management Methods
    loadSampleQuestions() {
        return [
            {
                id: 1,
                text: "What is the correct wire order for T568B standard?",
                type: "multiple-choice",
                level: 1,
                choices: ["Orange/White, Orange, Green/White, Blue, Blue/White, Green, Brown/White, Brown", "Green/White, Green, Orange/White, Blue, Blue/White, Orange, Brown/White, Brown"],
                correctAnswer: 0,
                explanation: "T568B uses the Orange/White, Orange, Green/White, Blue, Blue/White, Green, Brown/White, Brown sequence."
            },
            {
                id: 2,
                text: "A crossover cable connects two similar devices.",
                type: "true-false",
                level: 2,
                choices: ["True", "False"],
                correctAnswer: 0,
                explanation: "Crossover cables are used to connect similar devices like computer to computer or switch to switch."
            }
        ];
    }

    loadSampleLevels() {
        return [
            { id: 1, name: "Level 1 - Basic Cable Termination", description: "Introduction to basic cable termination concepts", difficulty: "beginner", position: 1 },
            { id: 2, name: "Level 2 - Intermediate Techniques", description: "Advanced wiring patterns and troubleshooting", difficulty: "intermediate", position: 2 },
            { id: 3, name: "Level 3 - Expert Challenges", description: "Complex scenarios and professional standards", difficulty: "advanced", position: 3 }
        ];
    }

    renderQuestions() {
        const container = document.getElementById('questions-list');
        container.innerHTML = '';

        this.questions.forEach(question => {
            const questionElement = this.createQuestionElement(question);
            container.appendChild(questionElement);
        });
    }

    createQuestionElement(question) {
        const div = document.createElement('div');
        div.className = 'question-item';
        div.dataset.questionId = question.id;

        div.innerHTML = `
            <div class="question-header">
                <div class="question-text">${question.text}</div>
            </div>
            <div class="question-meta">
                <span class="question-badge badge-level-${question.level}">Level ${question.level}</span>
                <span class="question-badge badge-${question.type}">${this.formatQuestionType(question.type)}</span>
            </div>
            <div class="question-controls">
                <button class="btn btn-small btn-secondary edit-question-btn" data-question-id="${question.id}">Edit</button>
                <button class="btn btn-small btn-danger delete-question-btn" data-question-id="${question.id}">Delete</button>
            </div>
        `;

        // Bind events for this question
        div.querySelector('.edit-question-btn').addEventListener('click', () => this.editQuestion(question.id));
        div.querySelector('.delete-question-btn').addEventListener('click', () => this.deleteQuestion(question.id));

        return div;
    }

    formatQuestionType(type) {
        const types = {
            'multiple-choice': 'Multiple Choice',
            'true-false': 'True/False',
            'practical': 'Practical'
        };
        return types[type] || type;
    }

    openQuestionEditor(questionId = null) {
        this.editingQuestion = questionId;
        this.isEditing = false;
        
        const modal = document.getElementById('question-editor-modal');
        const form = document.getElementById('question-form');
        const title = document.getElementById('editor-title');
        const saveBtn = document.getElementById('save-question');
        
        // Hide save button initially
        saveBtn.style.display = 'none';
        
        if (questionId) {
            const question = this.questions.find(q => q.id === questionId);
            title.textContent = 'Edit Question';
            this.populateQuestionForm(question);
        } else {
            title.textContent = 'Add New Question';
            form.reset();
            this.handleQuestionTypeChange('multiple-choice');
        }
        
        modal.classList.add('active');
    }

    populateQuestionForm(question) {
        document.getElementById('question-text').value = question.text;
        document.getElementById('question-type').value = question.type;
        document.getElementById('question-level').value = question.level;
        document.getElementById('explanation').value = question.explanation || '';
        
        this.handleQuestionTypeChange(question.type);
        
        // Populate choices
        if (question.choices) {
            const container = document.getElementById('choices-container');
            container.innerHTML = '';
            question.choices.forEach((choice, index) => {
                this.addChoice(choice);
            });
            document.getElementById('correct-answer').value = question.correctAnswer;
        }
    }

    handleQuestionTypeChange(type) {
        const choicesGroup = document.getElementById('choices-group');
        const container = document.getElementById('choices-container');
        
        if (type === 'true-false') {
            choicesGroup.style.display = 'none';
            // Set up true/false options
            const correctAnswer = document.getElementById('correct-answer');
            correctAnswer.innerHTML = '<option value="0">True</option><option value="1">False</option>';
        } else {
            choicesGroup.style.display = 'block';
            if (container.children.length === 0) {
                // Add default choices for multiple choice
                this.addChoice('');
                this.addChoice('');
            }
        }
    }

    addChoice(text = '') {
        const container = document.getElementById('choices-container');
        const index = container.children.length;
        
        const choiceDiv = document.createElement('div');
        choiceDiv.className = 'choice-item';
        choiceDiv.innerHTML = `
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <input type="text" class="choice-input" placeholder="Choice ${index + 1}" value="${text}" style="flex: 1;">
                <button type="button" class="btn btn-small btn-danger remove-choice-btn">Remove</button>
            </div>
        `;
        
        container.appendChild(choiceDiv);
        
        // Bind remove event
        choiceDiv.querySelector('.remove-choice-btn').addEventListener('click', () => {
            choiceDiv.remove();
            this.updateCorrectAnswerOptions();
            this.showSaveButton();
        });
        
        // Bind input event
        choiceDiv.querySelector('.choice-input').addEventListener('input', () => {
            this.updateCorrectAnswerOptions();
            this.showSaveButton();
        });
        
        this.updateCorrectAnswerOptions();
    }

    updateCorrectAnswerOptions() {
        const correctAnswer = document.getElementById('correct-answer');
        const choices = document.querySelectorAll('.choice-input');
        
        correctAnswer.innerHTML = '';
        choices.forEach((choice, index) => {
            if (choice.value.trim()) {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = choice.value;
                correctAnswer.appendChild(option);
            }
        });
    }

    showSaveButton() {
        if (!this.isEditing) {
            this.isEditing = true;
            document.getElementById('save-question').style.display = 'inline-block';
        }
    }

    saveQuestion(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const questionData = {
            text: document.getElementById('question-text').value,
            type: document.getElementById('question-type').value,
            level: parseInt(document.getElementById('question-level').value),
            explanation: document.getElementById('explanation').value
        };
        
        // Handle choices
        if (questionData.type === 'true-false') {
            questionData.choices = ['True', 'False'];
            questionData.correctAnswer = parseInt(document.getElementById('correct-answer').value);
        } else {
            const choices = Array.from(document.querySelectorAll('.choice-input')).map(input => input.value).filter(choice => choice.trim());
            questionData.choices = choices;
            questionData.correctAnswer = parseInt(document.getElementById('correct-answer').value);
        }
        
        if (this.editingQuestion) {
            // Update existing question
            const index = this.questions.findIndex(q => q.id === this.editingQuestion);
            questionData.id = this.editingQuestion;
            this.questions[index] = questionData;
        } else {
            // Add new question
            questionData.id = Date.now();
            this.questions.push(questionData);
        }
        
        this.renderQuestions();
        this.closeQuestionEditor();
        
        // Show success message
        this.showNotification('Question saved successfully!', 'success');
    }

    editQuestion(questionId) {
        this.openQuestionEditor(questionId);
    }

    deleteQuestion(questionId) {
        if (confirm('Are you sure you want to delete this question?')) {
            this.questions = this.questions.filter(q => q.id !== questionId);
            this.renderQuestions();
            this.showNotification('Question deleted successfully!', 'success');
        }
    }

    closeQuestionEditor() {
        document.getElementById('question-editor-modal').classList.remove('active');
        document.getElementById('question-form').reset();
        document.getElementById('save-question').style.display = 'none';
        this.editingQuestion = null;
        this.isEditing = false;
    }

    filterQuestions(searchTerm) {
        const questions = document.querySelectorAll('.question-item');
        questions.forEach(question => {
            const text = question.querySelector('.question-text').textContent.toLowerCase();
            if (text.includes(searchTerm.toLowerCase())) {
                question.style.display = 'block';
            } else {
                question.style.display = 'none';
            }
        });
    }

    filterQuestionsByLevel(level) {
        const questions = document.querySelectorAll('.question-item');
        questions.forEach(question => {
            if (!level || question.querySelector(`.badge-level-${level}`)) {
                question.style.display = 'block';
            } else {
                question.style.display = 'none';
            }
        });
    }

    // Level Management Methods
    renderLevels() {
        const container = document.getElementById('levels-list');
        // Keep the existing HTML structure as it already has the position controls
        
        // Bind events for level controls
        document.querySelectorAll('.edit-level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const levelId = parseInt(e.target.dataset.levelId);
                this.editLevel(levelId);
            });
        });
        
        document.querySelectorAll('.delete-level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const levelId = parseInt(e.target.dataset.levelId);
                this.deleteLevel(levelId);
            });
        });
        
        document.querySelectorAll('.move-level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const levelId = parseInt(e.target.dataset.levelId);
                const newPosition = parseInt(document.getElementById(`move-position-${levelId}`).value);
                this.moveLevel(levelId, newPosition);
            });
        });
    }

    moveLevel(levelId, newPosition) {
        // This implements the custom position movement feature
        const levelElement = document.querySelector(`[data-level-id="${levelId}"]`);
        const container = document.getElementById('levels-list');
        const allLevels = Array.from(container.children);
        
        // Remove the level from its current position
        levelElement.remove();
        
        // Insert at new position (convert to 0-based index)
        const targetIndex = newPosition - 1;
        if (targetIndex >= allLevels.length - 1) {
            container.appendChild(levelElement);
        } else {
            container.insertBefore(levelElement, allLevels[targetIndex]);
        }
        
        // Update position selectors for all levels
        this.updatePositionSelectors();
        
        this.showNotification(`Level moved to position ${newPosition}!`, 'success');
    }

    updatePositionSelectors() {
        const levels = document.querySelectorAll('.level-item');
        levels.forEach((level, index) => {
            const levelId = level.dataset.levelId;
            const selector = document.getElementById(`move-position-${levelId}`);
            
            // Clear and repopulate options
            selector.innerHTML = '';
            for (let i = 1; i <= levels.length; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === index + 1) option.selected = true;
                selector.appendChild(option);
            }
        });
    }

    openLevelEditor(levelId = null) {
        this.editingLevel = levelId;
        this.isEditing = false;
        
        const modal = document.getElementById('level-editor-modal');
        const form = document.getElementById('level-form');
        const title = document.getElementById('level-editor-title');
        const saveBtn = document.getElementById('save-level');
        
        // Hide save button initially
        saveBtn.style.display = 'none';
        
        if (levelId) {
            const level = this.levels.find(l => l.id === levelId);
            title.textContent = 'Edit Level';
            this.populateLevelForm(level);
        } else {
            title.textContent = 'Add New Level';
            form.reset();
        }
        
        modal.classList.add('active');
    }

    populateLevelForm(level) {
        document.getElementById('level-name').value = level.name;
        document.getElementById('level-description').value = level.description;
        document.getElementById('level-difficulty').value = level.difficulty;
    }

    showLevelSaveButton() {
        if (!this.isEditing) {
            this.isEditing = true;
            document.getElementById('save-level').style.display = 'inline-block';
        }
    }

    saveLevel(e) {
        e.preventDefault();
        
        const levelData = {
            name: document.getElementById('level-name').value,
            description: document.getElementById('level-description').value,
            difficulty: document.getElementById('level-difficulty').value
        };
        
        if (this.editingLevel) {
            // Update existing level
            const index = this.levels.findIndex(l => l.id === this.editingLevel);
            levelData.id = this.editingLevel;
            levelData.position = this.levels[index].position;
            this.levels[index] = levelData;
        } else {
            // Add new level
            levelData.id = Date.now();
            levelData.position = this.levels.length + 1;
            this.levels.push(levelData);
        }
        
        this.closeLevelEditor();
        this.showNotification('Level saved successfully!', 'success');
    }

    editLevel(levelId) {
        this.openLevelEditor(levelId);
    }

    deleteLevel(levelId) {
        if (confirm('Are you sure you want to delete this level?')) {
            this.levels = this.levels.filter(l => l.id !== levelId);
            document.querySelector(`[data-level-id="${levelId}"]`).remove();
            this.updatePositionSelectors();
            this.showNotification('Level deleted successfully!', 'success');
        }
    }

    closeLevelEditor() {
        document.getElementById('level-editor-modal').classList.remove('active');
        document.getElementById('level-form').reset();
        document.getElementById('save-level').style.display = 'none';
        this.editingLevel = null;
        this.isEditing = false;
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${type === 'success' ? '#27ae60' : '#3498db'};
            color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the management system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GameManagement();
});
