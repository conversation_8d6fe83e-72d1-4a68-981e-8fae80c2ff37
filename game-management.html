<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cable Game Management System</title>
    <link rel="stylesheet" href="management-styles.css">
</head>
<body>
    <div class="management-container">
        <header class="management-header">
            <h1>Cable Game Management System</h1>
            <nav class="management-nav">
                <button class="nav-btn active" data-section="questions">Question Management</button>
                <button class="nav-btn" data-section="levels">Level Management</button>
                <button class="nav-btn" data-section="settings">Settings</button>
            </nav>
        </header>

        <main class="management-content">
            <!-- Question Management Section -->
            <section id="questions-section" class="management-section active">
                <div class="section-header">
                    <h2>Question Management</h2>
                    <button class="btn btn-primary" id="add-question-btn">Add New Question</button>
                </div>

                <div class="questions-container">
                    <div class="question-filters">
                        <select id="level-filter">
                            <option value="">All Levels</option>
                            <option value="1">Level 1 - Basic</option>
                            <option value="2">Level 2 - Intermediate</option>
                            <option value="3">Level 3 - Advanced</option>
                        </select>
                        <input type="text" id="search-questions" placeholder="Search questions...">
                    </div>

                    <div class="questions-list" id="questions-list">
                        <!-- Questions will be dynamically loaded here -->
                    </div>
                </div>

                <!-- Question Editor Modal -->
                <div id="question-editor-modal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="editor-title">Edit Question</h3>
                            <button class="close-btn" id="close-editor">&times;</button>
                        </div>
                        <form id="question-form">
                            <div class="form-group">
                                <label for="question-text">Question Text:</label>
                                <textarea id="question-text" rows="3" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="question-type">Question Type:</label>
                                <select id="question-type" required>
                                    <option value="multiple-choice">Multiple Choice</option>
                                    <option value="true-false">True/False</option>
                                    <option value="practical">Practical Exercise</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="question-level">Level:</label>
                                <select id="question-level" required>
                                    <option value="1">Level 1 - Basic</option>
                                    <option value="2">Level 2 - Intermediate</option>
                                    <option value="3">Level 3 - Advanced</option>
                                </select>
                            </div>
                            <div class="form-group" id="choices-group">
                                <label>Answer Choices:</label>
                                <div id="choices-container">
                                    <!-- Dynamic choices will be added here -->
                                </div>
                                <button type="button" class="btn btn-secondary" id="add-choice-btn">Add Choice</button>
                            </div>
                            <div class="form-group">
                                <label for="correct-answer">Correct Answer:</label>
                                <select id="correct-answer" required>
                                    <!-- Options will be populated based on choices -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="explanation">Explanation:</label>
                                <textarea id="explanation" rows="2" placeholder="Optional explanation for the answer"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="cancel-edit">Cancel</button>
                                <button type="submit" class="btn btn-success" id="save-question" style="display: none;">Save Question</button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Level Management Section -->
            <section id="levels-section" class="management-section">
                <div class="section-header">
                    <h2>Level Management</h2>
                    <button class="btn btn-primary" id="add-level-btn">Add New Level</button>
                </div>

                <div class="levels-container">
                    <div class="levels-list" id="levels-list">
                        <!-- Sample level items will be dynamically loaded here -->
                        <div class="level-item" data-level-id="1">
                            <div class="level-info">
                                <h4>Level 1 - Basic Cable Termination</h4>
                                <p>Introduction to basic cable termination concepts</p>
                                <span class="level-badge beginner">Beginner</span>
                            </div>
                            <div class="level-controls">
                                <button class="btn btn-small btn-secondary edit-level-btn" data-level-id="1">Edit</button>
                                <div class="position-controls">
                                    <label for="move-position-1">Move to position:</label>
                                    <select class="position-selector" id="move-position-1" data-level-id="1">
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                    </select>
                                    <button class="btn btn-small btn-primary move-level-btn" data-level-id="1">Move</button>
                                </div>
                                <button class="btn btn-small btn-danger delete-level-btn" data-level-id="1">Delete</button>
                            </div>
                        </div>
                        <div class="level-item" data-level-id="2">
                            <div class="level-info">
                                <h4>Level 2 - Intermediate Techniques</h4>
                                <p>Advanced wiring patterns and troubleshooting</p>
                                <span class="level-badge intermediate">Intermediate</span>
                            </div>
                            <div class="level-controls">
                                <button class="btn btn-small btn-secondary edit-level-btn" data-level-id="2">Edit</button>
                                <div class="position-controls">
                                    <label for="move-position-2">Move to position:</label>
                                    <select class="position-selector" id="move-position-2" data-level-id="2">
                                        <option value="1">1</option>
                                        <option value="2" selected>2</option>
                                        <option value="3">3</option>
                                    </select>
                                    <button class="btn btn-small btn-primary move-level-btn" data-level-id="2">Move</button>
                                </div>
                                <button class="btn btn-small btn-danger delete-level-btn" data-level-id="2">Delete</button>
                            </div>
                        </div>
                        <div class="level-item" data-level-id="3">
                            <div class="level-info">
                                <h4>Level 3 - Expert Challenges</h4>
                                <p>Complex scenarios and professional standards</p>
                                <span class="level-badge advanced">Advanced</span>
                            </div>
                            <div class="level-controls">
                                <button class="btn btn-small btn-secondary edit-level-btn" data-level-id="3">Edit</button>
                                <div class="position-controls">
                                    <label for="move-position-3">Move to position:</label>
                                    <select class="position-selector" id="move-position-3" data-level-id="3">
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3" selected>3</option>
                                    </select>
                                    <button class="btn btn-small btn-primary move-level-btn" data-level-id="3">Move</button>
                                </div>
                                <button class="btn btn-small btn-danger delete-level-btn" data-level-id="3">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Level Editor Modal -->
                <div id="level-editor-modal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="level-editor-title">Edit Level</h3>
                            <button class="close-btn" id="close-level-editor">&times;</button>
                        </div>
                        <form id="level-form">
                            <div class="form-group">
                                <label for="level-name">Level Name:</label>
                                <input type="text" id="level-name" required>
                            </div>
                            <div class="form-group">
                                <label for="level-description">Description:</label>
                                <textarea id="level-description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="level-difficulty">Difficulty:</label>
                                <select id="level-difficulty" required>
                                    <option value="beginner">Beginner</option>
                                    <option value="intermediate">Intermediate</option>
                                    <option value="advanced">Advanced</option>
                                    <option value="expert">Expert</option>
                                </select>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="cancel-level-edit">Cancel</button>
                                <button type="submit" class="btn btn-success" id="save-level" style="display: none;">Save Level</button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="management-section">
                <div class="section-header">
                    <h2>Game Settings</h2>
                </div>
                <div class="settings-container">
                    <div class="setting-group">
                        <h3>General Settings</h3>
                        <div class="form-group">
                            <label for="game-title">Game Title:</label>
                            <input type="text" id="game-title" value="Cable Termination Game">
                        </div>
                        <div class="form-group">
                            <label for="max-attempts">Maximum Attempts per Question:</label>
                            <input type="number" id="max-attempts" value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label for="time-limit">Time Limit (minutes):</label>
                            <input type="number" id="time-limit" value="30" min="5" max="120">
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="management-script.js"></script>
</body>
</html>
